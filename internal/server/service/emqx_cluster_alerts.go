package service

import (
	"encoding/json"
	"fmt"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/model"
	"monitor/internal/shared/logger"
)

// checkEMQXClusterNodeAlerts 检查EMQX集群节点告警
func (ms *MonitorService) checkEMQXClusterNodeAlerts(serviceConfig config.ServiceConfig, clusterStatus *EMQXClusterStatus) {
	// 遍历所有节点，检查节点故障告警
	for nodeName, nodeStatus := range clusterStatus.NodeStatuses {
		if nodeStatus.Status == "unhealthy" {
			ms.triggerEMQXNodeDownAlert(serviceConfig, nodeName, nodeStatus.ErrorMessage)
		} else if nodeStatus.Status == "healthy" {
			ms.checkEMQXNodeRecoveryAlert(serviceConfig, nodeName)
		}
	}
}

// checkEMQXClusterAlerts 检查EMQX集群级别告警
func (ms *MonitorService) checkEMQXClusterAlerts(serviceConfig config.ServiceConfig, clusterStatus *EMQXClusterStatus) {
	// 检查集群降级告警
	if clusterStatus.ClusterHealth == "degraded" {
		ms.triggerEMQXClusterDegradedAlert(serviceConfig, clusterStatus)
	}

	// 检查集群严重故障告警
	if clusterStatus.ClusterHealth == "critical" {
		ms.triggerEMQXClusterCriticalAlert(serviceConfig, clusterStatus)
	}
}

// checkEMQXClusterConnectionFailureAlerts 检查EMQX集群连接失败告警
func (ms *MonitorService) checkEMQXClusterConnectionFailureAlerts(serviceConfig config.ServiceConfig, errorMsg string) {
	// 查找匹配的告警规则
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		// 检查规则是否适用于当前服务
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 检查是否是集群连接失败告警规则
		if rule.Condition.Type != "emqx_cluster_connection_failure" && rule.Condition.Type != "emqx_cluster_down" {
			continue
		}

		// 创建告警标签
		tags := map[string]string{
			"cluster":      serviceConfig.Name,
			"alert_type":   "cluster_connection_failure",
			"service_type": "emqx_cluster",
			"error_type":   "connection_refused",
		}

		// 将标签转换为JSON字符串
		tagsJSON, _ := json.Marshal(tags)

		// 创建告警
		alert := &model.Alert{
			RuleName:    rule.Name,
			ServiceName: serviceConfig.Name,
			Status:      model.AlertStatusFiring,
			Message:     fmt.Sprintf("EMQX集群连接失败: %s", errorMsg),
			Severity:    model.AlertSeverity(rule.Severity),
			Metadata:    string(tagsJSON),
		}

		// 检查是否已存在相同的告警
		existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
		if err == nil && existingAlert != nil {
			// 告警已存在，更新时间
			existingAlert.UpdatedAt = time.Now()
			if err := ms.alertRepo.Update(existingAlert); err != nil {
				logger.Error("更新EMQX集群连接失败告警失败",
					"service", serviceConfig.Name,
					"error", err)
			}
			continue
		}

		// 创建新告警
		if err := ms.alertRepo.Create(alert); err != nil {
			logger.Error("创建EMQX集群连接失败告警失败",
				"service", serviceConfig.Name,
				"error", err)
			continue
		}

		logger.Error("EMQX集群连接失败告警已触发",
			"service", serviceConfig.Name,
			"rule", rule.Name,
			"severity", rule.Severity,
			"error", errorMsg)

		// 发送通知（包括短信）
		if ms.notificationService != nil {
			if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
				logger.Error("发送EMQX集群连接失败告警通知失败",
					"service", serviceConfig.Name,
					"alert_id", alert.ID,
					"error", err)
			}
		}
	}
}

// triggerEMQXNodeDownAlert 触发节点故障告警
func (ms *MonitorService) triggerEMQXNodeDownAlert(serviceConfig config.ServiceConfig, nodeName, errorMsg string) {
	// 查找匹配的告警规则
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		// 检查规则是否适用于当前服务
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 检查是否是节点故障告警规则
		if rule.Condition.Type != "emqx_node_down" {
			continue
		}

		// 创建包含节点名称的唯一告警标识，确保每个节点的告警都有唯一标识
		alertKey := fmt.Sprintf("%s-%s", rule.Name, nodeName)

		// 创建告警标签
		tags := map[string]string{
			"node_name":    nodeName,
			"cluster":      serviceConfig.Name,
			"alert_type":   "node_down",
			"service_type": "emqx_cluster",
		}

		// 将标签转换为JSON字符串
		tagsJSON, _ := json.Marshal(tags)

		// 创建告警
		alert := &model.Alert{
			RuleName:    alertKey, // 使用包含节点名称的唯一标识
			ServiceName: serviceConfig.Name,
			Status:      model.AlertStatusFiring,
			Message:     fmt.Sprintf("EMQX节点 %s 故障: %s", nodeName, errorMsg),
			Severity:    model.AlertSeverity(rule.Severity),
			Metadata:    string(tagsJSON),
		}

		// 检查是否已存在相同的告警（使用包含节点名称的唯一标识）
		existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceConfig.Name)
		if err == nil && existingAlert != nil {
			// 告警已存在，更新时间
			existingAlert.UpdatedAt = time.Now()
			if err := ms.alertRepo.Update(existingAlert); err != nil {
				logger.Error("更新EMQX节点故障告警失败",
					"service", serviceConfig.Name,
					"node", nodeName,
					"error", err)
			}
			continue
		}

		// 创建新告警
		if err := ms.alertRepo.Create(alert); err != nil {
			logger.Error("创建EMQX节点故障告警失败",
				"service", serviceConfig.Name,
				"node", nodeName,
				"error", err)
			continue
		}

		logger.Error("EMQX节点故障告警已触发",
			"service", serviceConfig.Name,
			"node", nodeName,
			"rule", rule.Name,
			"severity", rule.Severity,
			"error", errorMsg)

		// 发送通知
		if ms.notificationService != nil {
			if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
				logger.Error("发送EMQX节点故障告警通知失败",
					"service", serviceConfig.Name,
					"node", nodeName,
					"alert_id", alert.ID,
					"error", err)
			}
		}
	}
}

// checkEMQXNodeRecoveryAlert 检查节点恢复告警
func (ms *MonitorService) checkEMQXNodeRecoveryAlert(serviceConfig config.ServiceConfig, nodeName string) {
	// 查找是否有活跃的节点故障告警
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		if rule.Condition.Type != "emqx_node_down" {
			continue
		}

		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 使用与创建告警时相同的唯一标识来查找对应节点的告警
		alertKey := fmt.Sprintf("%s-%s", rule.Name, nodeName)
		existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceConfig.Name)
		if err != nil || existingAlert == nil {
			continue
		}

		// 解决告警
		existingAlert.Status = model.AlertStatusResolved
		existingAlert.ResolvedAt = &time.Time{}
		*existingAlert.ResolvedAt = time.Now()

		if err := ms.alertRepo.Update(existingAlert); err != nil {
			logger.Error("解决EMQX节点故障告警失败",
				"service", serviceConfig.Name,
				"node", nodeName,
				"error", err)
			continue
		}

		logger.Info("EMQX节点故障告警已自动解决",
			"service", serviceConfig.Name,
			"node", nodeName,
			"rule", rule.Name,
			"alert_id", existingAlert.ID)

		// 发送恢复通知（如果配置了）
		if ms.notificationService != nil && ms.config.AutoResolve.SendNotification {
			// 创建恢复通知标签
			recoveryTags := map[string]string{
				"node_name":    nodeName,
				"cluster":      serviceConfig.Name,
				"alert_type":   "node_recovery",
				"service_type": "emqx_cluster",
			}
			recoveryTagsJSON, _ := json.Marshal(recoveryTags)

			// 创建恢复通知告警
			recoveryAlert := &model.Alert{
				RuleName:    fmt.Sprintf("%s_recovery", rule.Name),
				ServiceName: serviceConfig.Name,
				Status:      model.AlertStatusResolved,
				Message:     fmt.Sprintf("EMQX节点 %s 已恢复正常", nodeName),
				Severity:    model.AlertSeverityInfo,
				Metadata:    string(recoveryTagsJSON),
			}

			if err := ms.notificationService.SendAlertNotifications(recoveryAlert); err != nil {
				logger.Error("发送EMQX节点恢复通知失败",
					"service", serviceConfig.Name,
					"node", nodeName,
					"error", err)
			}
		}
	}
}

// triggerEMQXClusterDegradedAlert 触发集群降级告警
func (ms *MonitorService) triggerEMQXClusterDegradedAlert(serviceConfig config.ServiceConfig, clusterStatus *EMQXClusterStatus) {
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		if rule.Condition.Type != "emqx_cluster_degraded" {
			continue
		}

		// 检查是否已存在相同的告警
		existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
		if err == nil && existingAlert != nil {
			// 告警已存在，更新时间
			existingAlert.UpdatedAt = time.Now()
			if err := ms.alertRepo.Update(existingAlert); err != nil {
				logger.Error("更新EMQX集群降级告警失败",
					"service", serviceConfig.Name,
					"error", err)
			}
			continue
		}

		// 创建集群降级告警标签
		degradedTags := map[string]string{
			"cluster":        serviceConfig.Name,
			"alert_type":     "cluster_degraded",
			"service_type":   "emqx_cluster",
			"healthy_nodes":  fmt.Sprintf("%d", clusterStatus.HealthyNodes),
			"total_nodes":    fmt.Sprintf("%d", clusterStatus.TotalNodes),
			"cluster_health": clusterStatus.ClusterHealth,
		}
		degradedTagsJSON, _ := json.Marshal(degradedTags)

		// 创建集群降级告警
		alert := &model.Alert{
			RuleName:    rule.Name,
			ServiceName: serviceConfig.Name,
			Status:      model.AlertStatusFiring,
			Message: fmt.Sprintf("EMQX集群降级: %d/%d 节点健康 (健康率: %.1f%%)",
				clusterStatus.HealthyNodes,
				clusterStatus.TotalNodes,
				float64(clusterStatus.HealthyNodes)/float64(clusterStatus.TotalNodes)*100),
			Severity: model.AlertSeverity(rule.Severity),
			Metadata: string(degradedTagsJSON),
		}

		if err := ms.alertRepo.Create(alert); err != nil {
			logger.Error("创建EMQX集群降级告警失败",
				"service", serviceConfig.Name,
				"error", err)
			continue
		}

		logger.Warn("EMQX集群降级告警已触发",
			"service", serviceConfig.Name,
			"healthy_nodes", clusterStatus.HealthyNodes,
			"total_nodes", clusterStatus.TotalNodes,
			"cluster_health", clusterStatus.ClusterHealth)

		// 发送通知
		if ms.notificationService != nil {
			if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
				logger.Error("发送EMQX集群降级告警通知失败",
					"service", serviceConfig.Name,
					"alert_id", alert.ID,
					"error", err)
			}
		}
	}
}

// triggerEMQXClusterCriticalAlert 触发集群严重故障告警
func (ms *MonitorService) triggerEMQXClusterCriticalAlert(serviceConfig config.ServiceConfig, clusterStatus *EMQXClusterStatus) {
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		if rule.Condition.Type != "emqx_cluster_critical" {
			continue
		}

		// 检查是否已存在相同的告警
		existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
		if err == nil && existingAlert != nil {
			// 告警已存在，更新时间
			existingAlert.UpdatedAt = time.Now()
			if err := ms.alertRepo.Update(existingAlert); err != nil {
				logger.Error("更新EMQX集群严重故障告警失败",
					"service", serviceConfig.Name,
					"error", err)
			}
			continue
		}

		// 创建集群严重故障告警标签
		criticalTags := map[string]string{
			"cluster":        serviceConfig.Name,
			"alert_type":     "cluster_critical",
			"service_type":   "emqx_cluster",
			"healthy_nodes":  fmt.Sprintf("%d", clusterStatus.HealthyNodes),
			"total_nodes":    fmt.Sprintf("%d", clusterStatus.TotalNodes),
			"cluster_health": clusterStatus.ClusterHealth,
		}
		criticalTagsJSON, _ := json.Marshal(criticalTags)

		// 创建集群严重故障告警
		alert := &model.Alert{
			RuleName:    rule.Name,
			ServiceName: serviceConfig.Name,
			Status:      model.AlertStatusFiring,
			Message: fmt.Sprintf("EMQX集群严重故障: 仅有 %d/%d 节点健康，集群可能无法正常服务",
				clusterStatus.HealthyNodes,
				clusterStatus.TotalNodes),
			Severity: model.AlertSeverity(rule.Severity),
			Metadata: string(criticalTagsJSON),
		}

		if err := ms.alertRepo.Create(alert); err != nil {
			logger.Error("创建EMQX集群严重故障告警失败",
				"service", serviceConfig.Name,
				"error", err)
			continue
		}

		logger.Error("EMQX集群严重故障告警已触发",
			"service", serviceConfig.Name,
			"healthy_nodes", clusterStatus.HealthyNodes,
			"total_nodes", clusterStatus.TotalNodes,
			"cluster_health", clusterStatus.ClusterHealth)

		// 发送通知
		if ms.notificationService != nil {
			if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
				logger.Error("发送EMQX集群严重故障告警通知失败",
					"service", serviceConfig.Name,
					"alert_id", alert.ID,
					"error", err)
			}
		}
	}
}

// autoResolveEMQXClusterConnectionFailureAlerts 自动解决EMQX集群连接失败告警
func (ms *MonitorService) autoResolveEMQXClusterConnectionFailureAlerts(serviceConfig config.ServiceConfig) {
	// 查找连接失败相关的告警规则
	connectionFailureTypes := []string{
		"emqx_cluster_connection_failure",
		"emqx_cluster_down",
	}

	for _, alertType := range connectionFailureTypes {
		for _, rule := range ms.config.AlertRules {
			if !rule.Enabled {
				continue
			}

			if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
				continue
			}

			if rule.Condition.Type != alertType {
				continue
			}

			existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
			if err != nil || existingAlert == nil {
				continue
			}

			// 只解决正在触发的告警
			if existingAlert.Status != model.AlertStatusFiring {
				continue
			}

			// 解决告警
			existingAlert.Status = model.AlertStatusResolved
			existingAlert.ResolvedAt = &time.Time{}
			*existingAlert.ResolvedAt = time.Now()

			if err := ms.alertRepo.Update(existingAlert); err != nil {
				logger.Error("解决EMQX集群连接失败告警失败",
					"service", serviceConfig.Name,
					"alert_type", alertType,
					"error", err)
				continue
			}

			logger.Info("EMQX集群连接失败告警已自动解决",
				"service", serviceConfig.Name,
				"rule", rule.Name,
				"alert_type", alertType,
				"alert_id", existingAlert.ID)

			// 发送恢复通知（如果配置了）
			if ms.notificationService != nil && ms.config.AutoResolve.SendNotification {
				// 创建恢复通知标签
				recoveryTags := map[string]string{
					"cluster":      serviceConfig.Name,
					"alert_type":   fmt.Sprintf("%s_recovery", alertType),
					"service_type": "emqx_cluster",
				}
				recoveryTagsJSON, _ := json.Marshal(recoveryTags)

				recoveryAlert := &model.Alert{
					RuleName:    fmt.Sprintf("%s_recovery", rule.Name),
					ServiceName: serviceConfig.Name,
					Status:      model.AlertStatusResolved,
					Message:     fmt.Sprintf("EMQX集群连接已恢复正常，%s告警已解决", alertType),
					Severity:    model.AlertSeverityInfo,
					Metadata:    string(recoveryTagsJSON),
				}

				if err := ms.notificationService.SendAlertNotifications(recoveryAlert); err != nil {
					logger.Error("发送EMQX集群连接恢复通知失败",
						"service", serviceConfig.Name,
						"alert_type", alertType,
						"error", err)
				} else {
					logger.Info("EMQX集群连接恢复通知已发送",
						"service", serviceConfig.Name,
						"alert_type", alertType)
				}
			}
		}
	}
}

// autoResolveEMQXClusterAlerts 自动解决EMQX集群告警
func (ms *MonitorService) autoResolveEMQXClusterAlerts(serviceConfig config.ServiceConfig, clusterStatus *EMQXClusterStatus) {
	// 如果集群恢复健康，自动解决集群级别的告警
	if clusterStatus.ClusterHealth == "healthy" {
		ms.autoResolveEMQXClusterDegradedAlerts(serviceConfig)
		ms.autoResolveEMQXClusterCriticalAlerts(serviceConfig)
	}
}

// autoResolveEMQXClusterDegradedAlerts 自动解决集群降级告警
func (ms *MonitorService) autoResolveEMQXClusterDegradedAlerts(serviceConfig config.ServiceConfig) {
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		if rule.Condition.Type != "emqx_cluster_degraded" {
			continue
		}

		existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
		if err != nil || existingAlert == nil {
			continue
		}

		// 解决告警
		existingAlert.Status = model.AlertStatusResolved
		existingAlert.ResolvedAt = &time.Time{}
		*existingAlert.ResolvedAt = time.Now()

		if err := ms.alertRepo.Update(existingAlert); err != nil {
			logger.Error("解决EMQX集群降级告警失败",
				"service", serviceConfig.Name,
				"error", err)
			continue
		}

		logger.Info("EMQX集群降级告警已自动解决",
			"service", serviceConfig.Name,
			"rule", rule.Name,
			"alert_id", existingAlert.ID)

		// 发送恢复通知（如果配置了）
		if ms.notificationService != nil && ms.config.AutoResolve.SendNotification {
			// 创建恢复通知标签
			recoveryTags := map[string]string{
				"cluster":      serviceConfig.Name,
				"alert_type":   "cluster_recovery",
				"service_type": "emqx_cluster",
			}
			recoveryTagsJSON, _ := json.Marshal(recoveryTags)

			recoveryAlert := &model.Alert{
				RuleName:    fmt.Sprintf("%s_recovery", rule.Name),
				ServiceName: serviceConfig.Name,
				Status:      model.AlertStatusResolved,
				Message:     "EMQX集群已恢复健康状态",
				Severity:    model.AlertSeverityInfo,
				Metadata:    string(recoveryTagsJSON),
			}

			if err := ms.notificationService.SendAlertNotifications(recoveryAlert); err != nil {
				logger.Error("发送EMQX集群恢复通知失败",
					"service", serviceConfig.Name,
					"error", err)
			}
		}
	}
}

// autoResolveEMQXClusterCriticalAlerts 自动解决集群严重故障告警
func (ms *MonitorService) autoResolveEMQXClusterCriticalAlerts(serviceConfig config.ServiceConfig) {
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		if rule.Condition.Type != "emqx_cluster_critical" {
			continue
		}

		existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
		if err != nil || existingAlert == nil {
			continue
		}

		// 解决告警
		existingAlert.Status = model.AlertStatusResolved
		existingAlert.ResolvedAt = &time.Time{}
		*existingAlert.ResolvedAt = time.Now()

		if err := ms.alertRepo.Update(existingAlert); err != nil {
			logger.Error("解决EMQX集群严重故障告警失败",
				"service", serviceConfig.Name,
				"error", err)
			continue
		}

		logger.Info("EMQX集群严重故障告警已自动解决",
			"service", serviceConfig.Name,
			"rule", rule.Name,
			"alert_id", existingAlert.ID)

		// 发送恢复通知（如果配置了）
		if ms.notificationService != nil && ms.config.AutoResolve.SendNotification {
			// 创建恢复通知标签
			recoveryTags := map[string]string{
				"cluster":      serviceConfig.Name,
				"alert_type":   "cluster_critical_recovery",
				"service_type": "emqx_cluster",
			}
			recoveryTagsJSON, _ := json.Marshal(recoveryTags)

			recoveryAlert := &model.Alert{
				RuleName:    fmt.Sprintf("%s_recovery", rule.Name),
				ServiceName: serviceConfig.Name,
				Status:      model.AlertStatusResolved,
				Message:     "EMQX集群严重故障已恢复",
				Severity:    model.AlertSeverityInfo,
				Metadata:    string(recoveryTagsJSON),
			}

			if err := ms.notificationService.SendAlertNotifications(recoveryAlert); err != nil {
				logger.Error("发送EMQX集群严重故障恢复通知失败",
					"service", serviceConfig.Name,
					"error", err)
			}
		}
	}
}
