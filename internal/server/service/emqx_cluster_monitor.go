package service

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/repository"
	"monitor/internal/shared/logger"
)

// EMQXClusterMonitor EMQX集群监控器
type EMQXClusterMonitor struct {
	config           *config.EMQXClusterConfig
	serviceName      string
	managementClient *EMQXManagementClient
	metricsRepo      *repository.MetricsHistoryRepository

	// 集群状态
	clusterStatus EMQXClusterStatus
	nodeStatuses  map[string]*EMQXNodeStatus

	mu        sync.RWMutex
	lastCheck time.Time
}

// EMQXClusterStatus 集群状态
type EMQXClusterStatus struct {
	TotalNodes     int                        `json:"total_nodes"`
	HealthyNodes   int                        `json:"healthy_nodes"`
	UnhealthyNodes int                        `json:"unhealthy_nodes"`
	ClusterHealth  string                     `json:"cluster_health"` // "healthy", "degraded", "critical"
	NodeStatuses   map[string]*EMQXNodeStatus `json:"node_statuses"`
	LastUpdate     time.Time                  `json:"last_update"`
}

// EMQXNodeStatus 节点状态
type EMQXNodeStatus struct {
	NodeName     string           `json:"node_name"`
	Status       string           `json:"status"` // "healthy", "unhealthy", "unknown"
	IsConnected  bool             `json:"is_connected"`
	LastCheck    time.Time        `json:"last_check"`
	ErrorMessage string           `json:"error_message,omitempty"`
	Metrics      *EMQXNodeMetrics `json:"metrics,omitempty"`
}

// EMQXNodeMetrics 节点指标
type EMQXNodeMetrics struct {
	Connections     int64   `json:"connections"`
	Uptime          int64   `json:"uptime"`
	Load1           float64 `json:"load1"`
	Load5           float64 `json:"load5"`
	Load15          float64 `json:"load15"`
	MemoryUsed      int64   `json:"memory_used"`
	MemoryTotal     int64   `json:"memory_total"`
	MemoryUsageRate float64 `json:"memory_usage_rate"`
}

// NewEMQXClusterMonitor 创建EMQX集群监控器
func NewEMQXClusterMonitor(serviceName string, clusterConfig *config.EMQXClusterConfig, metricsRepo *repository.MetricsHistoryRepository) (*EMQXClusterMonitor, error) {
	if clusterConfig == nil {
		return nil, fmt.Errorf("集群配置不能为空")
	}

	// 创建管理API客户端
	managementClient, err := NewEMQXManagementClient(clusterConfig.ManagementAPI)
	if err != nil {
		return nil, fmt.Errorf("创建管理API客户端失败: %w", err)
	}

	// 测试管理API连接
	if err := managementClient.CheckAPIHealth(); err != nil {
		logger.Warn("管理API健康检查失败，但继续创建监控器",
			"service", serviceName,
			"host", clusterConfig.ManagementAPI.Host,
			"port", clusterConfig.ManagementAPI.Port,
			"error", err)
	} else {
		logger.Info("管理API健康检查成功",
			"service", serviceName,
			"host", clusterConfig.ManagementAPI.Host,
			"port", clusterConfig.ManagementAPI.Port)
	}

	monitor := &EMQXClusterMonitor{
		config:           clusterConfig,
		serviceName:      serviceName,
		managementClient: managementClient,
		metricsRepo:      metricsRepo,
		nodeStatuses:     make(map[string]*EMQXNodeStatus),
		clusterStatus: EMQXClusterStatus{
			NodeStatuses: make(map[string]*EMQXNodeStatus),
		},
	}

	logger.Info("EMQX集群监控器创建成功",
		"service", serviceName,
		"management_host", clusterConfig.ManagementAPI.Host,
		"management_port", clusterConfig.ManagementAPI.Port)

	return monitor, nil
}

// CheckClusterHealth 检查集群健康状态
func (cm *EMQXClusterMonitor) CheckClusterHealth() (*EMQXClusterStatus, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	logger.Debug("开始检查EMQX集群健康状态", "service", cm.serviceName)

	// 获取集群节点列表
	nodes, err := cm.managementClient.GetClusterNodes()
	if err != nil {
		return nil, fmt.Errorf("获取集群节点失败: %w", err)
	}

	if len(nodes) == 0 {
		return nil, fmt.Errorf("集群中没有发现任何节点")
	}

	healthyNodes := 0
	totalNodes := len(nodes)
	nodeStatuses := make(map[string]*EMQXNodeStatus)

	// 检查每个节点状态
	for _, node := range nodes {
		nodeStatus := cm.checkNodeHealth(node)
		nodeStatuses[node.Node] = nodeStatus

		if nodeStatus.Status == "healthy" {
			healthyNodes++
		}

		logger.Debug("节点状态检查完成",
			"service", cm.serviceName,
			"node", node.Node,
			"status", nodeStatus.Status,
			"connections", nodeStatus.Metrics.Connections)
	}

	// 计算集群健康状态
	clusterHealth := cm.calculateClusterHealth(healthyNodes, totalNodes)

	// 更新集群状态
	cm.clusterStatus = EMQXClusterStatus{
		TotalNodes:     totalNodes,
		HealthyNodes:   healthyNodes,
		UnhealthyNodes: totalNodes - healthyNodes,
		ClusterHealth:  clusterHealth,
		NodeStatuses:   nodeStatuses,
		LastUpdate:     time.Now(),
	}

	cm.nodeStatuses = nodeStatuses
	cm.lastCheck = time.Now()

	logger.Info("EMQX集群健康检查完成",
		"service", cm.serviceName,
		"total_nodes", totalNodes,
		"healthy_nodes", healthyNodes,
		"cluster_health", clusterHealth)

	return &cm.clusterStatus, nil
}

// checkNodeHealth 检查单个节点健康状态
func (cm *EMQXClusterMonitor) checkNodeHealth(nodeInfo EMQXNodeInfo) *EMQXNodeStatus {
	now := time.Now()

	// 解析内存信息（从字符串格式如"4.94G"转换）
	memoryUsed := cm.parseMemorySize(nodeInfo.MemoryUsed)
	memoryTotal := cm.parseMemorySize(nodeInfo.MemoryTotal)

	// 计算内存使用率
	memoryUsageRate := float64(0)
	if memoryTotal > 0 {
		memoryUsageRate = float64(memoryUsed) / float64(memoryTotal) * 100
	}

	nodeStatus := &EMQXNodeStatus{
		NodeName:    nodeInfo.Node,
		IsConnected: nodeInfo.NodeStatus == "running",
		LastCheck:   now,
		Metrics: &EMQXNodeMetrics{
			Connections:     nodeInfo.Connections,
			Uptime:          nodeInfo.Uptime,
			Load1:           nodeInfo.Load1,
			Load5:           nodeInfo.Load5,
			Load15:          nodeInfo.Load15,
			MemoryUsed:      memoryUsed,
			MemoryTotal:     memoryTotal,
			MemoryUsageRate: memoryUsageRate,
		},
	}

	// 根据节点状态设置健康状态
	if nodeInfo.NodeStatus == "running" {
		nodeStatus.Status = "healthy"
		nodeStatus.ErrorMessage = ""
	} else {
		nodeStatus.Status = "unhealthy"
		nodeStatus.ErrorMessage = fmt.Sprintf("节点状态: %s", nodeInfo.NodeStatus)
	}

	return nodeStatus
}

// calculateClusterHealth 计算集群健康状态
func (cm *EMQXClusterMonitor) calculateClusterHealth(healthyNodes, totalNodes int) string {
	if totalNodes == 0 {
		return "unknown"
	}

	healthyRatio := float64(healthyNodes) / float64(totalNodes)

	if healthyNodes == totalNodes {
		return "healthy"
	} else if healthyRatio >= 0.5 {
		return "degraded"
	} else {
		return "critical"
	}
}

// GetClusterStatus 获取当前集群状态
func (cm *EMQXClusterMonitor) GetClusterStatus() EMQXClusterStatus {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.clusterStatus
}

// GetNodeStatus 获取指定节点状态
func (cm *EMQXClusterMonitor) GetNodeStatus(nodeName string) (*EMQXNodeStatus, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	status, exists := cm.nodeStatuses[nodeName]
	return status, exists
}

// IsHealthy 判断集群是否健康
func (cm *EMQXClusterMonitor) IsHealthy() bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.clusterStatus.ClusterHealth == "healthy"
}

// parseMemorySize 解析内存大小字符串（如"4.94G"）转换为字节数
func (cm *EMQXClusterMonitor) parseMemorySize(sizeStr string) int64 {
	if sizeStr == "" {
		return 0
	}

	// 移除空格
	sizeStr = strings.TrimSpace(sizeStr)
	if len(sizeStr) == 0 {
		return 0
	}

	// 获取单位（最后一个字符）
	unit := strings.ToUpper(string(sizeStr[len(sizeStr)-1]))
	valueStr := sizeStr[:len(sizeStr)-1]

	// 解析数值
	value, err := strconv.ParseFloat(valueStr, 64)
	if err != nil {
		logger.Warn("解析内存大小失败", "size_str", sizeStr, "error", err)
		return 0
	}

	// 根据单位转换为字节
	switch unit {
	case "B":
		return int64(value)
	case "K":
		return int64(value * 1024)
	case "M":
		return int64(value * 1024 * 1024)
	case "G":
		return int64(value * 1024 * 1024 * 1024)
	case "T":
		return int64(value * 1024 * 1024 * 1024 * 1024)
	default:
		// 如果没有单位，假设是字节
		return int64(value)
	}
}

// Close 关闭集群监控器
func (cm *EMQXClusterMonitor) Close() error {
	if cm.managementClient != nil {
		return cm.managementClient.Close()
	}
	return nil
}
