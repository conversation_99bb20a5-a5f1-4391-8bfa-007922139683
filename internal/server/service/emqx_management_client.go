package service

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"
)

// EMQXManagementClient EMQX管理API客户端
type EMQXManagementClient struct {
	config     config.EMQXManagementAPIConfig
	httpClient *http.Client
	baseURL    string
}

// EMQXNodeInfo EMQX节点信息
type EMQXNodeInfo struct {
	Node        string  `json:"node"`         // 节点名称
	NodeStatus  string  `json:"node_status"`  // 节点状态: running, stopped
	Uptime      int64   `json:"uptime"`       // 运行时间（毫秒）
	Version     string  `json:"version"`      // EMQX版本
	Connections int64   `json:"connections"`  // 连接数
	Load1       float64 `json:"load1"`        // 1分钟负载
	Load5       float64 `json:"load5"`        // 5分钟负载
	Load15      float64 `json:"load15"`       // 15分钟负载
	MaxFds      int64   `json:"max_fds"`      // 最大文件描述符
	MemoryUsed  string  `json:"memory_used"`  // 已使用内存（字符串格式，如"4.94G"）
	MemoryTotal string  `json:"memory_total"` // 总内存（字符串格式，如"15.51G"）
	Role        string  `json:"role"`         // 节点角色
	Edition     string  `json:"edition"`      // 版本类型
	OtpRelease  string  `json:"otp_release"`  // OTP版本
}

// 注意：EMQX v5 API直接返回节点数组，不需要包装结构体

// NewEMQXManagementClient 创建EMQX管理API客户端
func NewEMQXManagementClient(config config.EMQXManagementAPIConfig) (*EMQXManagementClient, error) {
	if config.Host == "" {
		return nil, fmt.Errorf("管理API主机不能为空")
	}

	if config.Port == 0 {
		config.Port = 18083 // 默认端口
	}

	// 构建基础URL
	scheme := "http"
	if config.UseHTTPS {
		scheme = "https"
	}
	baseURL := fmt.Sprintf("%s://%s:%d", scheme, config.Host, config.Port)

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	client := &EMQXManagementClient{
		config:     config,
		baseURL:    baseURL,
		httpClient: httpClient,
	}

	logger.Info("EMQX管理API客户端创建成功",
		"host", config.Host,
		"port", config.Port,
		"use_https", config.UseHTTPS)

	return client, nil
}

// GetClusterNodes 获取集群节点列表
func (c *EMQXManagementClient) GetClusterNodes() ([]EMQXNodeInfo, error) {
	url := fmt.Sprintf("%s/api/v5/nodes", c.baseURL)

	req, err := c.createAuthenticatedRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("连接管理API失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("管理API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	logger.Debug("EMQX API响应", "body", string(body))

	// 根据您的测试结果，EMQX API直接返回数组格式
	var nodes []EMQXNodeInfo
	if err := json.Unmarshal(body, &nodes); err != nil {
		return nil, fmt.Errorf("解析节点列表失败: %w, 响应: %s", err, string(body))
	}

	logger.Info("获取集群节点成功", "node_count", len(nodes))
	return nodes, nil
}

// GetNodeStats 获取节点统计信息
func (c *EMQXManagementClient) GetNodeStats(nodeName string) (*EMQXNodeInfo, error) {
	url := fmt.Sprintf("%s/api/v5/nodes/%s", c.baseURL, nodeName)

	req, err := c.createAuthenticatedRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("连接管理API失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("获取节点统计失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var nodeInfo EMQXNodeInfo
	if err := json.NewDecoder(resp.Body).Decode(&nodeInfo); err != nil {
		return nil, fmt.Errorf("解析节点统计失败: %w", err)
	}

	return &nodeInfo, nil
}

// CheckAPIHealth 检查管理API健康状态
func (c *EMQXManagementClient) CheckAPIHealth() error {
	url := fmt.Sprintf("%s/api/v5/nodes", c.baseURL)

	req, err := c.createAuthenticatedRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建健康检查请求失败: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("健康检查请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("管理API不健康，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	logger.Debug("管理API健康检查成功", "url", url)
	return nil
}

// createAuthenticatedRequest 创建带认证的HTTP请求
func (c *EMQXManagementClient) createAuthenticatedRequest(method, url string, body io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	// 验证API密钥
	if c.config.Auth.APIKey == "" || c.config.Auth.SecretKey == "" {
		return nil, fmt.Errorf("API密钥和Secret密钥不能为空")
	}

	// 使用Basic Auth方式，用户名为API Key，密码为Secret Key
	req.SetBasicAuth(c.config.Auth.APIKey, c.config.Auth.SecretKey)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	return req, nil
}

// Close 关闭客户端
func (c *EMQXManagementClient) Close() error {
	// HTTP客户端不需要显式关闭
	return nil
}
