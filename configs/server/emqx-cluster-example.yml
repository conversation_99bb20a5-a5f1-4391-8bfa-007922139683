# EMQX集群监控配置示例
# 本文件展示如何配置EMQX集群健康监控功能

monitor:
  # 基础服务配置
  server:
    port: 8080

  # 数据库配置
  database:
    host: *************
    port: 5432
    database: vdm
    username: vdm
    password: vDm#2023@postGre
    schema: monitor
    max_connections: 20
    ssl_mode: disable

  # 日志配置
  logging:
    level: "info"
    format: "console"

  # 全局Agent默认配置
  agent_defaults:
    port: 9090
    token: "secure-token-123"
    timeout: 30
    retry_count: 3

  # 监控默认配置
  monitor_defaults:
    type: "http"
    method: "GET"
    check_interval: 30
    timeout: 10
    retry_count: 3
    expected_status: [200, 201, 204]
    health_path: "/health/check"

  # 自动解决配置
  auto_resolve:
    enabled: true
    healthy_checks_required: 2
    send_notification: true

  # ==================== 服务监控配置 ====================
  services:
    # EMQX集群健康监控（新功能）
    - name: "emqx-cluster-health"
      type: "emqx_cluster"                    # 新的监控类型
      check_interval: 30                      # 集群检查间隔
      emqx_cluster:
        management_api:
          host: "emqx-api.company.com"        # 管理API负载均衡器地址
          port: 18083                         # 管理API端口
          use_https: true                     # 生产环境建议使用HTTPS
          auth:
            api_key: "${EMQX_API_KEY}"        # API密钥
            secret_key: "${EMQX_SECRET_KEY}"  # Secret密钥
        timeout: 30                           # 连接超时时间
        retry_count: 3                        # 重试次数

    # EMQX主题监控（保持独立）
    - name: "emqx-topics"
      type: "emqx"                            # 原有的主题监控类型
      check_interval: 60
      emqx:
        host: "emqx-mqtt.company.com"         # MQTT负载均衡器地址
        port: 1883
        topics:
          - "device/+/status"                 # 设备状态主题
          - "vehicle/+/location"              # 车辆位置主题
          - "sensor/+/data"                   # 传感器数据主题
        rate_window: 300                      # 5分钟速率窗口
        baseline_window: 1800                 # 30分钟基线窗口
        min_baseline_rate: 10                 # 最小基线速率
        auth:
          username: "monitor"
          password: "${EMQX_MONITOR_PASSWORD}"
        timeout: 30
        retry_count: 3

    # 其他服务示例
    - name: "api-gateway"
      type: "http"
      endpoint: "http://api-gateway.company.com/health"
      check_interval: 30
      timeout: 10

  # ==================== 告警规则配置 ====================
  alert_rules:
    # EMQX集群节点故障告警
    - name: "emqx_node_failure"
      services: ["emqx-cluster-health"]       # 适用于集群健康监控服务
      condition:
        type: "emqx_node_down"                # 节点故障告警类型
        threshold: 1                          # 任何节点故障都告警
      severity: "critical"                    # 严重级别
      enabled: true

    # EMQX集群降级告警
    - name: "emqx_cluster_degraded"
      services: ["emqx-cluster-health"]
      condition:
        type: "emqx_cluster_degraded"         # 集群降级告警类型
        threshold: 50                         # 健康节点少于50%时告警
      severity: "warning"                     # 警告级别
      enabled: true

    # EMQX集群严重故障告警
    - name: "emqx_cluster_critical"
      services: ["emqx-cluster-health"]
      condition:
        type: "emqx_cluster_critical"         # 集群严重故障告警类型
        threshold: 1                          # 只有1个或0个健康节点时告警
      severity: "critical"                    # 严重级别
      enabled: true

    # EMQX主题速率下降告警（原有功能）
    - name: "emqx_topic_rate_drop"
      services: ["emqx-topics"]
      condition:
        type: "emqx_rate_drop"                # 主题速率下降告警
        threshold: 30                         # 速率下降30%时告警
      severity: "warning"
      enabled: true

    # HTTP服务故障告警
    - name: "http_service_down"
      services: ["api-gateway"]
      condition:
        type: "consecutive_failures"
        threshold: 3
      severity: "critical"
      enabled: true

  # ==================== 通知配置 ====================
  notifications:
    # 通知渠道配置
    channels:
      # 邮件通知
      email:
        url: "http://mail-service.company.com/api/send"
        timeout_seconds: 30

      # 短信通知
      sms:
        url: "http://sms-service.company.com/api/send"
        username: "monitor"
        password: "${SMS_PASSWORD}"
        timeout_seconds: 30

      # Webhook通知
      webhook:
        url: "http://alert-webhook.company.com/api/alerts"
        timeout: 15
        retry_count: 3

    # 通知分组
    groups:
      # 基础设施团队（负责EMQX集群）
      - name: "infrastructure-team"
        services: ["emqx-cluster-health", "emqx-topics"]
        channels: ["email", "sms", "webhook"]
        recipients:
          email:
            - "<EMAIL>"
            - "<EMAIL>"
          sms:
            - "13800138000"                   # 基础设施负责人
            - "13900139000"                   # 值班人员
        escalation:
          - level: 1
            delay: "0m"
            channels: ["webhook"]             # 立即通知监控系统
          - level: 2
            delay: "5m"                       # 5分钟后邮件通知
            channels: ["email"]
          - level: 3
            delay: "15m"                      # 15分钟后短信通知
            channels: ["sms"]

      # 应用开发团队
      - name: "application-team"
        services: ["api-gateway"]
        channels: ["email"]
        recipients:
          email:
            - "<EMAIL>"
        escalation:
          - level: 1
            delay: "0m"
            channels: ["email"]

    # 告警抑制配置
    suppression:
      rules: []

# ==================== 配置说明 ====================
#
# 1. EMQX集群监控特性：
#    - 自动发现集群中的所有节点
#    - 监控每个节点的健康状态
#    - 检测集群整体健康状况
#    - 节点故障时立即告警
#    - 节点恢复时自动解除告警
#
# 2. 认证方式：
#    - 使用EMQX API密钥认证（API Key + Secret Key）
#    - 敏感信息使用环境变量存储
#
# 3. 告警类型：
#    - emqx_node_down: 节点故障告警
#    - emqx_cluster_degraded: 集群降级告警
#    - emqx_cluster_critical: 集群严重故障告警
#
# 4. 监控分离：
#    - 集群健康监控：emqx_cluster类型
#    - 主题速率监控：emqx类型（原有功能）
#
# 5. 高可用设计：
#    - 通过负载均衡器访问管理API
#    - 支持HTTPS加密传输
#    - 多级告警通知机制
#
# 6. 环境变量：
#    - EMQX_API_KEY: EMQX管理API密钥（334debcfbdc435a8）
#    - EMQX_SECRET_KEY: EMQX Secret密钥（wpKjm9C0MBQH37LRpHmuJy4uuNDtKDUpFfXrZIxtYzoD）
#    - EMQX_MONITOR_PASSWORD: MQTT监控用户密码
#    - SMS_PASSWORD: 短信服务密码
