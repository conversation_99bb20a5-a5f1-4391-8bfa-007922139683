# EMQX集群监控测试配置
# 用于测试新实现的EMQX集群健康监控功能

monitor:
  # 基础服务配置
  server:
    port: 8080

  # 数据库配置
  database:
    host: localhost
    port: 5432
    database: vdm
    username: vdm
    password: vDm#2023@postGre
    schema: monitor
    max_connections: 20
    ssl_mode: disable

  # 日志配置
  logging:
    level: "debug"                    # 使用debug级别便于观察
    format: "console"

  # 全局Agent默认配置
  agent_defaults:
    port: 9090
    token: "test-token-123"
    timeout: 30
    retry_count: 3

  # 监控默认配置
  monitor_defaults:
    type: "http"
    method: "GET"
    check_interval: 30
    timeout: 10
    retry_count: 3
    expected_status: [200, 201, 204]
    health_path: "/health/check"

  # 自动解决配置
  auto_resolve:
    enabled: true
    healthy_checks_required: 2
    send_notification: true

  # ==================== 服务监控配置 ====================
  services:
    # EMQX集群健康监控（测试配置）
    - name: "emqx-cluster-test"
      type: "emqx_cluster"                    # 新的监控类型
      check_interval: 30                      # 30秒检查一次
      emqx_cluster:
        management_api:
          host: "localhost"                   # 测试环境使用localhost
          port: 18083                         # EMQX管理API默认端口
          use_https: false                    # 测试环境使用HTTP
          auth:
            api_key: "334debcfbdc435a8"       # 测试API密钥
            secret_key: "wpKjm9C0MBQH37LRpHmuJy4uuNDtKDUpFfXrZIxtYzoD"  # 测试Secret密钥
        timeout: 30                           # 连接超时时间
        retry_count: 3                        # 重试次数

    # 可选：EMQX主题监控（用于对比测试）
    - name: "emqx-topics-test"
      type: "emqx"                            # 原有的主题监控类型
      check_interval: 60
      emqx:
        host: "localhost"                     # MQTT连接地址
        port: 1883                            # MQTT端口
        topics:
          - "test/+/data"                     # 测试主题
          - "device/+/status"                 # 设备状态主题
        rate_window: 300                      # 5分钟速率窗口
        baseline_window: 1800                 # 30分钟基线窗口
        min_baseline_rate: 1                  # 测试环境较低的基线
        auth:
          username: "test"                    # 测试用户
          password: "test123"                 # 测试密码
        timeout: 30
        retry_count: 3

  # ==================== 告警规则配置 ====================
  alert_rules:
    # EMQX集群节点故障告警
    - name: "emqx_cluster_node_failure_test"
      services: ["emqx-cluster-test"]         # 适用于测试集群
      condition:
        type: "emqx_node_down"                # 节点故障告警类型
        threshold: 1                          # 任何节点故障都告警
      severity: "critical"                    # 严重级别
      enabled: true

    # EMQX集群降级告警
    - name: "emqx_cluster_degraded_test"
      services: ["emqx-cluster-test"]
      condition:
        type: "emqx_cluster_degraded"         # 集群降级告警类型
        threshold: 50                         # 健康节点少于50%时告警
      severity: "warning"                     # 警告级别
      enabled: true

    # EMQX集群严重故障告警
    - name: "emqx_cluster_critical_test"
      services: ["emqx-cluster-test"]
      condition:
        type: "emqx_cluster_critical"         # 集群严重故障告警类型
        threshold: 1                          # 只有1个或0个健康节点时告警
      severity: "critical"                    # 严重级别
      enabled: true

  # ==================== 通知配置 ====================
  notifications:
    # 通知渠道配置（测试环境简化）
    channels:
      # 控制台输出（用于测试）
      webhook:
        url: "http://localhost:8080/test/webhook"  # 测试webhook地址
        timeout: 15
        retry_count: 1

    # 通知分组（测试环境）
    groups:
      - name: "test-team"
        services: ["emqx-cluster-test", "emqx-topics-test"]
        channels: ["webhook"]
        recipients:
          webhook:
            - "test-endpoint"
        escalation:
          - level: 1
            delay: "0m"
            channels: ["webhook"]             # 立即通知

    # 告警抑制配置
    suppression:
      rules: []

# ==================== 测试说明 ====================
#
# 1. 启动测试：
#    - 确保EMQX服务正在运行（localhost:18083管理API可访问）
#    - 使用此配置启动监控服务
#    - 观察日志输出，查看集群监控是否正常工作
#
# 2. 测试场景：
#    - 正常情况：所有EMQX节点运行正常
#    - 故障模拟：停止一个EMQX节点，观察告警
#    - 恢复测试：重启节点，观察告警自动解除
#
# 3. 验证要点：
#    - 集群节点自动发现是否正常
#    - 节点健康状态检查是否准确
#    - 告警触发和恢复是否及时
#    - 指标数据是否正确存储
#
# 4. 日志观察：
#    - 查看"EMQX集群健康检查"相关日志
#    - 观察节点状态变化
#    - 确认告警创建和解决过程
#
# 5. 数据库验证：
#    - 检查monitor.service_health表中的集群状态
#    - 查看monitor.metrics_history表中的集群指标
#    - 确认monitor.alerts表中的告警记录
